using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ConcurrentCollections;

namespace MaoYouJi
{
  [EntitySystem]
  [FriendOf(typeof(BagComponent))]
  public static partial class BagGiveSystem
  {
    public static bool HasEnoughCapacity(this BagComponent bag, int num)
    {
      return bag.thingMap.Count + num <= bag.capacity;
    }

    /**
    * 计算需要剩余的容量
    *
    * @param things 物品列表
    * @return 需要剩余的容量
    */
    public static int NeedRemainCapacity(this BagComponent bag, List<Thing> things)
    {
      Dictionary<ThingNameEnum, List<Thing>> thingMap = new();
      foreach (Thing thing in things)
      {
        if (!thingMap.TryGetValue(thing.thingName, out List<Thing> newThings))
        {
          newThings = new List<Thing>();
          thingMap.Add(thing.thingName, newThings);
        }
        newThings.Add(thing);
      }
      if (thingMap.Count == 0)
      {
        return 0;
      }
      HashSet<ThingNameEnum> thingNames = new(thingMap.Keys);
      foreach (ThingNameEnum thingName in thingNames)
      {
        List<Thing> newThings = thingMap[thingName];
        if (!bag.nameIdMap.TryGetValue(thingName, out ConcurrentHashSet<long> thingIds))
        {
          continue;
        }
        foreach (long thingId in thingIds)
        {
          if (bag.thingMap.TryGetValue(thingId, out Thing thing))
          {
            newThings.RemoveAll(newThing => thing.grade == newThing.grade && thing.ownType == newThing.ownType
                && thing.num < newThing.stackNum);
          }
        }
        if (newThings.Count == 0)
        {
          thingMap.Remove(thingName);
        }
      }
      return thingMap.Count;
    }

    /**
   * 移除一个物品
   *
   * @param thingId 物品id
   */
    public static void RemoveOneThing(this BagComponent bag, long thingId)
    {
      if (!bag.thingMap.TryGetValue(thingId, out Thing thing))
      {
        return;
      }
      bag.RemoveThing(thing);
    }

    public static void RemoveThings(this BagComponent bag, List<long> thingIds)
    {
      foreach (long thingId in thingIds)
      {
        bag.RemoveOneThing(thingId);
      }
    }

    // 移除物品
    public static void RemoveThing(this BagComponent bag, Thing thing)
    {
      User user = bag.GetParent<User>();
      bag.thingMap.TryRemove(thing.thingId, out _);
      bag.bagIdLock.EnterWriteLock();
      bag.thingIds.Remove(thing.thingId);
      bag.bagIdLock.ExitWriteLock();
      bag.nameIdMap.TryGetValue(thing.thingName, out ConcurrentHashSet<long> thingIds);
      thingIds.TryRemove(thing.thingId);
      if (thingIds.Count == 0)
      {
        bag.nameIdMap.TryRemove(thing.thingName, out _);
      }
      if (user != null)
      {
        AttackComponent attackComponent = user.GetComponent<AttackComponent>();
        SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
        if (skillComponent.RemoveQuickFood(thing.thingId))
        {
          user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Quick_Bar));
        }
      }
    }
    /**
   * 移除物品列表
   *
   * @param thingNames 物品名称列表
   */
    public static void RemoveThingsWithSend(this BagComponent bag, List<ThingNameEnum> thingNames)
    {
      User user = bag.GetParent<User>();
      ServerUpdateBagMsg updateBagThingsOut = new ServerUpdateBagMsg();
      foreach (ThingNameEnum thingName in thingNames)
      {
        bag.nameIdMap.TryGetValue(thingName, out ConcurrentHashSet<long> thingIds);
        if (thingIds == null)
        {
          continue;
        }
        foreach (long thingId in thingIds)
        {
          bag.thingMap.TryGetValue(thingId, out Thing thing);
          if (thing == null)
          {
            continue;
          }
          bag.RemoveThing(thing);
          user?.SendChat("背包中的" + thing.name + "被移除了");
          updateBagThingsOut.removeThingIds.Add(thingId);
        }
      }
      if (updateBagThingsOut.removeThingIds.Count == 0 && updateBagThingsOut.updateList.Count == 0)
      {
        return;
      }
      user?.SendMessage(updateBagThingsOut);
    }

    public static void RemoveGiveThingWithSend(this BagComponent bag, List<ThingGiveInfo> thingGiveInfos)
    {
      User user = bag.GetParent<User>();
      ServerUpdateBagMsg updateBagThingsOut = new();
      foreach (ThingGiveInfo thingGiveInfo in thingGiveInfos)
      {
        bag.nameIdMap.TryGetValue(thingGiveInfo.thingName, out ConcurrentHashSet<long> thingIds);
        List<Thing> things = new();
        if (thingIds == null)
        {
          continue;
        }
        foreach (long thingId in thingIds)
        {
          bag.thingMap.TryGetValue(thingId, out Thing thing);
          if (thing == null)
          {
            continue;
          }
          if (thingGiveInfo.ownType != OwnType.None && thing.ownType != thingGiveInfo.ownType)
          {
            continue;
          }
          things.Add(thing);
        }
        foreach (Thing thing in things)
        {
          if (thingGiveInfo.num <= 0)
          {
            break;
          }
          if (thing.num > thingGiveInfo.num)
          {
            user?.SendChat("背包中的" + thing.name + "数量减少" + thingGiveInfo.num);
            thing.num -= thingGiveInfo.num;
            thingGiveInfo.num = 0;
            updateBagThingsOut.updateList.Add(thing);
          }
          else
          {
            user?.SendChat("背包中的" + thing.name + "数量减少" + thing.num);
            bag.RemoveThing(thing);
            thingGiveInfo.num -= thing.num;
            thing.num = 0;
            updateBagThingsOut.removeThingIds.Add(thing.thingId);
          }
        }
      }
      if (updateBagThingsOut.removeThingIds.Count == 0 && updateBagThingsOut.updateList.Count == 0)
      {
        return;
      }
      user?.SendMessage(updateBagThingsOut);
    }

    // 添加物品
    public static void AddThing(this BagComponent bag, Thing thing)
    {
      if (thing.thingId == 0 || bag.thingMap.ContainsKey(thing.thingId))
      {
        thing.thingId = IdGenerater.Instance.GenerateId();
      }
      bag.thingMap.TryAdd(thing.thingId, thing);

      bag.bagIdLock.EnterWriteLock();
      bag.thingIds.Add(thing.thingId);
      bag.bagIdLock.ExitWriteLock();

      bag.nameIdMap.TryAdd(thing.thingName, new ConcurrentHashSet<long>());
      bag.nameIdMap[thing.thingName].Add(thing.thingId);
    }

    // 增加物品数量
    public static void AddThingNumWithSend(this BagComponent bag, Thing thing, int num)
    {
      thing.num += num;
      ServerUpdateBagMsg updateBagThingsOut = new();
      if (thing.num <= 0)
      {
        bag.RemoveThing(thing);
        updateBagThingsOut.removeThingIds.Add(thing.thingId);
      }
      else
      {
        updateBagThingsOut.updateList.Add(thing);
      }
      User user = bag.GetParent<User>();
      if (num < 0)
      {
        user?.SendChat("背包中的" + thing.name + "数量减少" + Math.Abs(num));
      }
      else
      {
        user?.SendChat("背包中的" + thing.name + "数量增加" + Math.Abs(num));
      }
      ETLog.Info($"addThingNumWithSend, {bag.ownId}, {thing.thingName}, {thing.grade}, {thing.ownType}, {num}, {thing.num}");
      user?.SendMessage(updateBagThingsOut);
    }

    public static void SendNewCoin(this BagComponent bag)
    {
      User user = bag.GetParent<User>();
      ServerUpdateBagMsg outParams = new()
      {
        isCoin = true,
        coinNum = bag.coin,
        catEyeNum = bag.catEye,
        catBeanNum = bag.catBean,
        packCapNum = bag.capacity
      };
      user?.SendMessage(outParams);
    }

    public static void AddAllCoinWithSend(this BagComponent bag, long coinNum, long catBeanNum = 0, long catEyeNum = 0)
    {
      User user = bag.GetParent<User>();
      bag.coin += coinNum;
      bag.catBean += catBeanNum;
      bag.catEye += catEyeNum;
      string content = "";
      if (coinNum > 0)
      {
        content += BagProcSys.GetCoinNumStr(coinNum) + "";
      }
      if (catBeanNum > 0)
      {
        content += content.Length > 0 ? "," : "";
        content += catBeanNum + "猫豆";
      }
      if (catEyeNum > 0)
      {
        content += content.Length > 0 ? "," : "";
        content += catEyeNum + "猫眼";
      }
      bag.SendNewCoin();
      user?.SendChat("您获得了" + content);
    }

    /**
   * 给予物品列表
   *
   * @param things 物品列表
   */
    public static void GiveThingList(this BagComponent bag, List<Thing> things)
    {
      User user = bag.GetParent<User>();
      ServerUpdateBagMsg updateBagThingsOut = new();
      ServerSendChatMsg sendChatOut = new();
      sendChatOut.chatType = ChatType.Sys_Chat;
      Dictionary<ThingNameEnum, List<Thing>> thingMap = new();
      List<Thing> needAddThings = new();
      foreach (Thing thing in things)
      {
        thingMap.TryAdd(thing.thingName, new List<Thing>());
        thingMap[thing.thingName].Add(thing);
      }
      sendChatOut.content = "您获得了"
          + things.Select(thing => thing.name + "x" + thing.num).Aggregate((a, b) => a + "," + b);
      HashSet<ThingNameEnum> thingNames = new(thingMap.Keys);
      // 判断是否添加成功
      foreach (ThingNameEnum thingName in thingNames)
      {
        List<Thing> newThings = thingMap[thingName];
        List<Thing> addSuccessThings = new();
        bag.nameIdMap.TryGetValue(thingName, out ConcurrentHashSet<long> thingIds);
        if (thingIds != null)
        {
          foreach (long thingId in thingIds)
          {
            bag.thingMap.TryGetValue(thingId, out Thing thing);
            if (thing == null)
            {
              continue;
            }
            foreach (Thing newThing in newThings)
            {
              if (thing.grade != newThing.grade || thing.ownType != newThing.ownType || thing.num >= thing.stackNum)
              {
                continue;
              }
              thing.num += newThing.num;
              updateBagThingsOut.updateList.Add(thing);
              addSuccessThings.Add(newThing);
            }
          }
        }
        newThings.RemoveAll(addSuccessThings.Contains);
        if (newThings.Count == 0)
        {
          thingMap.Remove(thingName);
        }
      }
      // 添加物品
      foreach (List<Thing> newThings in thingMap.Values)
      {
        needAddThings.AddRange(newThings);
      }
      foreach (Thing thing in needAddThings)
      {
        if (bag.nameIdMap.Count >= bag.capacity)
        {
          user?.SendChat("背包已满，您未能获得" + thing.name);
          ETLog.Info($"loseThing, {bag.ownId}, {thing.thingName}, {thing.ownType}, {thing.grade}, {thing.num}");
          continue;
        }
        bag.AddThing(thing);
        updateBagThingsOut.updateList.Add(thing);
      }
      user?.SendMessage(sendChatOut, updateBagThingsOut);
    }

    // 给予物品
    public static Thing GiveThing(this BagComponent bag, ThingGiveInfo giveInfo, ThingFromType fromType = ThingFromType.None)
    {
      List<Thing> things = bag.GiveThing(new List<ThingGiveInfo> { giveInfo }, fromType);
      return things.FirstOrDefault();
    }

    // 给予物品，这个函数会自动发送最新的背包信息
    public static List<Thing> GiveThing(this BagComponent bag, List<ThingGiveInfo> giveInfos, ThingFromType fromType = ThingFromType.None)
    {
      List<Thing> things = new();
      User user = bag.GetParent<User>();
      // 猫眼和猫豆单独处理
      List<ThingGiveInfo> catBeanAndEyeGiveInfos = giveInfos.Where(giveInfo => giveInfo.thingName == ThingNameEnum.Cat_Bean || giveInfo.thingName == ThingNameEnum.Cat_Eye).ToList();
      giveInfos.RemoveAll(catBeanAndEyeGiveInfos.Contains);
      int catBean = 0, catEye = 0;
      foreach (ThingGiveInfo giveInfo in catBeanAndEyeGiveInfos)
      {
        if (giveInfo.thingName == ThingNameEnum.Cat_Bean)
        {
          catBean += giveInfo.num;
        }
        else if (giveInfo.thingName == ThingNameEnum.Cat_Eye)
        {
          catEye += giveInfo.num;
        }
      }
      if (catBean > 0 || catEye > 0)
      {
        bag.AddAllCoinWithSend(0, catBean, catEye);
      }
      // 处理其他物品
      Dictionary<ThingNameEnum, Thing> baseThings = GlobalInfoCache.Instance.GetBaseThingList(
          giveInfos.Select(giveInfo => giveInfo.thingName).ToList(), true);
      StringBuilder sb = new();
      foreach (ThingGiveInfo giveInfo in giveInfos)
      {
        Thing thing = bag.GetThingInBag<Thing>(giveInfo.thingName, ThingGrade.GOOD, giveInfo.ownType);
        Thing newThing = null;
        if (thing == null || thing.num >= thing.stackNum || thing.stackNum <= 1)
        {
          baseThings.TryGetValue(giveInfo.thingName, out newThing);
          if (newThing == null)
          {
            ETLog.Error($"物品不存在: {giveInfo.thingName}");
            continue;
          }
          if (giveInfo.ownType != OwnType.None)
          {
            newThing.ownType = giveInfo.ownType;
          }
          if (newThing.ownType == OwnType.None)
          {
            newThing.ownType = OwnType.PUBLIC;
          }
          if (newThing.thingType == ThingType.EQUIP)
          {
            Equipment equip = newThing as Equipment;
            equip?.ProcNewEquip();
          }
          newThing.thingId = IdGenerater.Instance.GenerateId();
          if (newThing.stackNum <= 1)
          {
            newThing.num = 1;
            for (int i = 0; i < giveInfo.num; i++)
            {
              if (bag.thingIds.Count >= bag.capacity)
              {
                user?.SendChat("背包已满，您无法获得" + newThing.name);
                ETLog.Info($"canNotGetThing, {bag.ownId}, {giveInfo.thingName}, {giveInfo.ownType}, {giveInfo.num}, {newThing.num}");
                break;
              }
              if (user != null)
              {
                EventSystem.Instance.Publish(bag.Scene(), new GetThingEvent()
                {
                  user = user,
                  thing = newThing,
                  num = 1,
                  fromType = fromType,
                });
              }
              ETLog.Info($"giveThing, {bag.ownId}, {giveInfo.thingName}, {giveInfo.ownType}, {giveInfo.num}, {newThing.num}");
              sb.Append(newThing.name + " x" + newThing.num + ",");
              bag.AddThing(newThing);
              things.Add(newThing);
              if (i < giveInfo.num - 1)
              {
                newThing = newThing.Clone() as Thing;
                newThing.thingId = IdGenerater.Instance.GenerateId();
              }
            }
          }
          else
          {
            if (bag.thingIds.Count >= bag.capacity)
            {
              user?.SendChat("背包已满，您无法获得" + newThing.name + " x" + giveInfo.num);
              ETLog.Info($"canNotGetThing, {bag.ownId}, {giveInfo.thingName}, {giveInfo.ownType}, {giveInfo.num}, {newThing.num}");
              continue;
            }
            if (user != null)
            {
              EventSystem.Instance.Publish(bag.Scene(), new GetThingEvent()
              {
                user = user,
                thing = newThing,
                num = giveInfo.num,
                fromType = fromType,
              });
            }
            bag.AddThing(newThing);
            things.Add(newThing);
            sb.Append(newThing.name + " x" + giveInfo.num + ",");
            ETLog.Info($"giveThing, {bag.ownId}, {giveInfo.thingName}, {giveInfo.ownType}, {giveInfo.num}, {newThing.num}");
          }
        }
        else
        {
          if (user != null)
          {
            EventSystem.Instance.Publish(bag.Scene(), new GetThingEvent()
            {
              user = user,
              thing = thing,
              num = giveInfo.num,
              fromType = fromType,
            });
          }
          bag.AddThingNumWithSend(thing, giveInfo.num);
          things.Add(thing);
          sb.Append(thing.name + " x" + giveInfo.num + ",");
          ETLog.Info($"giveThing, {bag.ownId}, {giveInfo.thingName}, {giveInfo.ownType}, {giveInfo.num}, {thing.num}");
        }
      }
      if (sb.Length > 0)
      {
        sb.Remove(sb.Length - 1, 1);
        user?.SendChat("您获得了" + sb.ToString());
      }
      ServerUpdateBagMsg updateBagThingsOut = new();
      updateBagThingsOut.updateList.AddRange(things);
      user?.SendMessage(updateBagThingsOut);
      return things;
    }

    /// <summary>
    /// 检查材料是否足够
    /// </summary>
    /// <param name="bag">背包组件</param>
    /// <param name="needThings">需要的材料列表</param>
    /// <returns>检查结果</returns>
    public static LogicRet CheckMaterials(this BagComponent bag, List<ThingGiveInfo> needThings)
    {
      if (needThings == null || needThings.Count == 0)
      {
        return LogicRet.Success;
      }

      foreach (ThingGiveInfo needThing in needThings)
      {
        int totalNum = bag.GetThingTotalNum(needThing.thingName, ThingGrade.None, needThing.ownType);
        if (totalNum < needThing.num)
        {
          return LogicRet.Failed($"材料 {needThing.thingName} 不足，需要 {needThing.num} 个，当前拥有 {totalNum} 个");
        }
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 扣除材料并发送背包更新消息
    /// </summary>
    /// <param name="bag">背包组件</param>
    /// <param name="needThings">需要扣除的材料列表</param>
    /// <returns>扣除结果</returns>
    public static LogicRet ConsumeMaterials(this BagComponent bag, List<ThingGiveInfo> needThings)
    {
      if (needThings == null || needThings.Count == 0)
      {
        return LogicRet.Success;
      }

      User user = bag.GetParent<User>();
      ServerUpdateBagMsg updateBagMsg = new ServerUpdateBagMsg();
      List<Thing> thingsToRemove = new List<Thing>();
      List<string> chatMessages = new List<string>(); // 存储聊天消息

      try
      {
        foreach (ThingGiveInfo needThing in needThings)
        {
          // 获取背包中的物品
          List<Thing> things = bag.GetThingInBag(needThing.thingName, needThing.ownType);
          if (things == null || things.Count == 0)
          {
            return LogicRet.Failed($"背包中没有找到材料 {needThing.thingName}");
          }

          int remainingNeed = needThing.num;
          int totalConsumed = 0; // 记录总共消耗的数量

          // 按数量扣除物品
          foreach (Thing thing in things)
          {
            if (remainingNeed <= 0) break;

            int consumedFromThisThing = 0; // 记录从这个物品中消耗的数量

            if (thing.num >= remainingNeed)
            {
              consumedFromThisThing = remainingNeed;
              thing.num -= remainingNeed;
              totalConsumed += remainingNeed;
              remainingNeed = 0;

              if (thing.num <= 0)
              {
                thingsToRemove.Add(thing);
                updateBagMsg.removeThingIds.Add(thing.thingId);
                // 物品被完全移除
                chatMessages.Add($"{thing.name} 已移除");
              }
              else
              {
                updateBagMsg.updateList.Add(thing);
                // 物品数量减少但未移除
                chatMessages.Add($"{thing.name} -{consumedFromThisThing}");
              }
            }
            else
            {
              consumedFromThisThing = thing.num;
              remainingNeed -= thing.num;
              totalConsumed += thing.num;
              thing.num = 0;
              thingsToRemove.Add(thing);
              updateBagMsg.removeThingIds.Add(thing.thingId);
              // 物品被完全移除
              chatMessages.Add($"{thing.name} 已移除");
            }
          }

          if (remainingNeed > 0)
          {
            return LogicRet.Failed($"材料 {needThing.thingName} 数量不足");
          }
        }

        // 移除数量为0的物品
        foreach (Thing thing in thingsToRemove)
        {
          bag.RemoveThing(thing);
          ETLog.Info($"ConsumeMaterials remove thing: {bag.ownId}, {thing.thingName}, {thing.ownType}, {thing.grade}");
        }

        // 发送背包更新消息
        if (updateBagMsg.removeThingIds.Count > 0 || updateBagMsg.updateList.Count > 0)
        {
          user?.SendMessage(updateBagMsg);
        }

        // 发送聊天消息通知物品变化
        if (chatMessages.Count > 0 && user != null)
        {
          // 将消息分组，避免单条消息过长
          const int maxItemsPerMessage = 5; // 每条消息最多显示5个物品变化
          for (int i = 0; i < chatMessages.Count; i += maxItemsPerMessage)
          {
            var messageGroup = chatMessages.Skip(i).Take(maxItemsPerMessage);
            string content = "背包物品变化：" + string.Join("，", messageGroup);
            user.SendChat(content);
          }
        }

        return LogicRet.Success;
      }
      catch (System.Exception ex)
      {
        ETLog.Error($"扣除材料时发生错误: {ex.Message}");
        return LogicRet.Failed("扣除材料时发生内部错误");
      }
    }
  }
}